/* Reset básico */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dialer-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.dialer {
    background: white;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    max-width: 300px;
    width: 100%;
}

/* Display del número */
.display {
    margin-bottom: 25px;
}

#numberDisplay {
    width: 100%;
    padding: 15px;
    font-size: 24px;
    text-align: center;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    background: #f8f9fa;
    color: #333;
    font-weight: 500;
}

#numberDisplay:focus {
    outline: none;
    border-color: #667eea;
}

/* Teclado numérico */
.keypad {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 25px;
}

.key {
    width: 60px;
    height: 60px;
    border: none;
    border-radius: 50%;
    background: #f1f3f4;
    font-size: 20px;
    font-weight: 600;
    color: #333;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.key:hover {
    background: #e8eaed;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.key:active {
    transform: translateY(0);
    background: #dadce0;
}

.backspace {
    background: #ff6b6b;
    color: white;
    font-size: 18px;
}

.backspace:hover {
    background: #ff5252;
}

/* Botón de llamada */
.call-section {
    display: flex;
    justify-content: center;
}

.call-btn {
    background: #4caf50;
    color: white;
    border: none;
    padding: 15px 40px;
    font-size: 18px;
    font-weight: 600;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.call-btn:hover {
    background: #45a049;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.call-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(76, 175, 80, 0.3);
}

.call-btn:disabled {
    background: #cccccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* Responsive */
@media (max-width: 480px) {
    .dialer {
        padding: 20px;
        margin: 10px;
    }
    
    .key {
        width: 50px;
        height: 50px;
        font-size: 18px;
    }
    
    #numberDisplay {
        font-size: 20px;
        padding: 12px;
    }
}
