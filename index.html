<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phone Dialer</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="dialer-container">
        <div class="dialer">
            <!-- Display del número -->
            <div class="display">
                <input type="text" id="numberDisplay" readonly placeholder="Ingresa un número">
            </div>

            <!-- Teclado numérico -->
            <div class="keypad">
                <button class="key" data-digit="1">1</button>
                <button class="key" data-digit="2">2</button>
                <button class="key" data-digit="3">3</button>
                
                <button class="key" data-digit="4">4</button>
                <button class="key" data-digit="5">5</button>
                <button class="key" data-digit="6">6</button>
                
                <button class="key" data-digit="7">7</button>
                <button class="key" data-digit="8">8</button>
                <button class="key" data-digit="9">9</button>
                
                <button class="key" data-digit="+">+</button>
                <button class="key" data-digit="0">0</button>
                <button class="key backspace" data-action="backspace">←</button>
            </div>

            <!-- Botón de llamada -->
            <div class="call-section">
                <button id="callButton" class="call-btn">Call</button>
            </div>
        </div>
    </div>

    <script src="main.js"></script>
</body>
</html>
