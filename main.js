// ============================================
// CONFIGURACIÓN DE LA API
// ============================================
// TODO: Reemplazar con la URL real de tu backend .NET
const API_BASE_URL = 'https://your-dotnet-api.com'; // <-- CAMBIAR AQUÍ
const CALL_ENDPOINT = `${API_BASE_URL}/api/call`;

// ============================================
// VARIABLES GLOBALES
// ============================================
let currentNumber = '';
const numberDisplay = document.getElementById('numberDisplay');
const callButton = document.getElementById('callButton');

// ============================================
// INICIALIZACIÓN
// ============================================
document.addEventListener('DOMContentLoaded', function() {
    initializeDialer();
});

function initializeDialer() {
    // Configurar eventos del teclado
    setupKeypadEvents();
    
    // Configurar evento del botón de llamada
    setupCallButton();
    
    console.log('Phone Dialer inicializado correctamente');
}

// ============================================
// MANEJO DEL TECLADO NUMÉRICO
// ============================================
function setupKeypadEvents() {
    const keys = document.querySelectorAll('.key');
    
    keys.forEach(key => {
        key.addEventListener('click', function() {
            const digit = this.getAttribute('data-digit');
            const action = this.getAttribute('data-action');
            
            if (digit) {
                addDigit(digit);
            } else if (action === 'backspace') {
                removeLastDigit();
            }
        });
    });
}

function addDigit(digit) {
    // Limitar la longitud del número (máximo 15 dígitos)
    if (currentNumber.length < 15) {
        currentNumber += digit;
        updateDisplay();
    }
}

function removeLastDigit() {
    if (currentNumber.length > 0) {
        currentNumber = currentNumber.slice(0, -1);
        updateDisplay();
    }
}

function updateDisplay() {
    numberDisplay.value = currentNumber;
    
    // Habilitar/deshabilitar botón de llamada
    callButton.disabled = currentNumber.length === 0;
}

// ============================================
// FUNCIONALIDAD DE LLAMADA
// ============================================
function setupCallButton() {
    callButton.addEventListener('click', function() {
        if (currentNumber.trim() !== '') {
            makeCall();
        }
    });
    
    // Inicialmente deshabilitado
    callButton.disabled = true;
}

async function makeCall() {
    const numberToCall = currentNumber.trim();
    
    if (!numberToCall) {
        alert('Por favor ingresa un número válido');
        return;
    }
    
    console.log(`Iniciando llamada a: ${numberToCall}`);
    
    // Deshabilitar botón durante la llamada
    callButton.disabled = true;
    callButton.textContent = 'Calling...';
    
    try {
        // ============================================
        // LLAMADA A LA API .NET
        // ============================================
        const response = await fetch(CALL_ENDPOINT, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                // TODO: Agregar headers de autenticación si es necesario
                // 'Authorization': 'Bearer YOUR_TOKEN_HERE'
            },
            body: JSON.stringify({
                number: numberToCall
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        
        // Registrar respuesta en consola
        console.log('Respuesta de la API:', result);
        console.log(`Llamada exitosa a ${numberToCall}`);
        
        // Limpiar el número después de una llamada exitosa
        clearNumber();
        
    } catch (error) {
        console.error('Error al realizar la llamada:', error);
        
        // Mostrar error básico al usuario
        alert('Error al llamar. Por favor intenta nuevamente.');
        
    } finally {
        // Restaurar estado del botón
        callButton.disabled = currentNumber.length === 0;
        callButton.textContent = 'Call';
    }
}

// ============================================
// FUNCIONES AUXILIARES
// ============================================
function clearNumber() {
    currentNumber = '';
    updateDisplay();
}

// ============================================
// SOPORTE PARA TECLADO FÍSICO (BONUS)
// ============================================
document.addEventListener('keydown', function(event) {
    const key = event.key;
    
    // Números 0-9
    if (key >= '0' && key <= '9') {
        addDigit(key);
        event.preventDefault();
    }
    // Signo más
    else if (key === '+') {
        addDigit('+');
        event.preventDefault();
    }
    // Backspace
    else if (key === 'Backspace') {
        removeLastDigit();
        event.preventDefault();
    }
    // Enter para llamar
    else if (key === 'Enter' && currentNumber.length > 0) {
        makeCall();
        event.preventDefault();
    }
});

// ============================================
// NOTAS PARA INTEGRACIÓN CON BACKEND .NET
// ============================================
/*
PASOS PARA CONECTAR CON TU API .NET:

1. Cambiar API_BASE_URL por la URL real de tu backend
2. Si tu API requiere autenticación, descomenta y configura el header Authorization
3. Ajustar el formato del JSON si tu API espera un formato diferente
4. Manejar respuestas específicas de tu API en el bloque try/catch

EJEMPLO DE CONFIGURACIÓN:
const API_BASE_URL = 'https://localhost:5001'; // Para desarrollo local
const API_BASE_URL = 'https://tu-api.azurewebsites.net'; // Para producción

EJEMPLO CON AUTENTICACIÓN:
headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + localStorage.getItem('authToken')
}
*/
