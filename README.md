# 📱 Phone Dialer - Frontend Ultra-Simple

Frontend minimalista de marcador telefónico diseñado para integrarse con APIs .NET de llamadas.

## 🚀 Características

- **Interfaz limpia**: Display readonly + teclado numérico + botón de llamada
- **Vanilla JavaScript**: Sin frameworks pesados, solo HTML/CSS/JS
- **Responsive**: Funciona en desktop y móvil
- **Teclado físico**: Soporte para teclas numéricas, +, Backspace y Enter
- **Ready-to-use**: Solo 3 archivos, abre directamente en navegador

## 📁 Estructura

```
├── index.html      # Estructura HTML del dialer
├── styles.css      # Estilos CSS con Flexbox
├── main.js         # Lógica JavaScript y llamadas API
└── README.md       # Este archivo
```

## 🎯 Funcionalidad

1. **Marcación**: Botones 1-9, 0, + y ← (backspace)
2. **Display**: Muestra el número conforme se marca
3. **Llamada**: Botón verde "Call" ejecuta POST a la API
4. **Validación**: Máximo 15 dígitos, botón se deshabilita sin número

## 🔧 Cómo ejecutar

### Opción 1: Doble clic
Simplemente abre `index.html` en tu navegador.

### Opción 2: Servidor local
```bash
npx serve .
# Abre http://localhost:3000
```

### Opción 3: Live Server (VSCode)
Clic derecho en `index.html` → "Open with Live Server"

## 🔌 Conectar con Backend .NET

### 1. Configurar URL de la API

Edita `main.js` línea 5:

```javascript
// Cambiar esta línea:
const API_BASE_URL = 'https://your-dotnet-api.com';

// Por tu URL real:
const API_BASE_URL = 'https://localhost:5001';              // Desarrollo
const API_BASE_URL = 'https://tu-api.azurewebsites.net';    // Producción
```

### 2. Endpoint esperado

El frontend hace POST a `/api/call` con este JSON:

```json
{
  "number": "1234567890"
}
```

### 3. Ejemplo de Controller .NET

```csharp
[ApiController]
[Route("api")]
public class CallController : ControllerBase
{
    [HttpPost("call")]
    public async Task<IActionResult> MakeCall([FromBody] CallRequest request)
    {
        // Tu lógica de llamada aquí
        var result = await _callService.InitiateCall(request.Number);
        
        return Ok(new { 
            success = true, 
            callId = result.CallId,
            message = "Call initiated successfully" 
        });
    }
}

public class CallRequest
{
    public string Number { get; set; }
}
```

### 4. Configurar CORS (si es necesario)

En tu `Program.cs` o `Startup.cs`:

```csharp
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowFrontend", policy =>
    {
        policy.WithOrigins("http://localhost:3000", "file://")
              .AllowAnyHeader()
              .AllowAnyMethod();
    });
});

app.UseCors("AllowFrontend");
```

### 5. Autenticación (opcional)

Si tu API requiere autenticación, descomenta en `main.js`:

```javascript
headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + localStorage.getItem('authToken')
}
```

## 🐛 Debugging

- **Consola del navegador** (F12): Muestra respuestas de la API y errores
- **Network tab**: Inspecciona las peticiones HTTP
- **Errores de CORS**: Configurar CORS en el backend
- **URL incorrecta**: Verificar que la API esté corriendo

## 📊 Especificaciones técnicas

- **Líneas de código**: ~180 total
- **Dependencias**: Ninguna
- **Compatibilidad**: Navegadores modernos (ES6+)
- **Tamaño**: < 10KB total

---

**Desarrollado para integración con APIs .NET de llamadas telefónicas**
